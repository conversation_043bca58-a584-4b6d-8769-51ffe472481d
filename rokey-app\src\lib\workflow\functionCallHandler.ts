// Function Call Handler
// Processes AI model function calls and executes the appropriate tools

import { getFunctionByName, TOOL_FUNCTION_MAP } from './functionSchemas';
import {
  GoogleDriveAPI,
  GoogleDocsAPI,
  GoogleSheetsAPI,
  GmailAPI
} from './toolImplementations';
import {
  GoogleCalendarAPI,
  YouTubeAPI,
  NotionAPI
} from './toolImplementations2';
import { WebBrowsingTool } from '@/lib/tools/webBrowsing';

export interface FunctionCall {
  name: string;
  arguments: string | Record<string, any>;
}

export interface FunctionCallResult {
  success: boolean;
  result?: any;
  error?: string;
  functionName: string;
  executionTime: number;
}

export class FunctionCallHandler {
  private userId: string;
  private timeout: number;

  constructor(userId: string, timeout: number = 600000) {
    this.userId = userId;
    this.timeout = timeout;
  }

  /**
   * Get connected tools for the current user
   */
  private async getConnectedTools(): Promise<string[]> {
    try {
      const { getConnectedTools } = await import('@/lib/oauth/middleware');
      return await getConnectedTools(this.userId);
    } catch (error) {
      console.error('Error getting connected tools:', error);
      return [];
    }
  }

  /**
   * Execute a function call from AI model with comprehensive error handling
   */
  async executeFunctionCall(functionCall: FunctionCall): Promise<FunctionCallResult> {
    const startTime = Date.now();

    try {
      // Validate function call structure
      if (!functionCall || !functionCall.name) {
        throw new Error('Invalid function call: missing function name');
      }

      // Parse arguments if they're a string
      let args: Record<string, any>;
      if (typeof functionCall.arguments === 'string') {
        try {
          args = JSON.parse(functionCall.arguments);
        } catch (e) {
          throw new Error(`Invalid function arguments JSON: ${functionCall.arguments}. Error: ${e instanceof Error ? e.message : 'Unknown JSON parse error'}`);
        }
      } else if (typeof functionCall.arguments === 'object' && functionCall.arguments !== null) {
        args = functionCall.arguments;
      } else {
        throw new Error(`Invalid function arguments type: ${typeof functionCall.arguments}`);
      }

      // Validate function exists
      const functionSchema = getFunctionByName(functionCall.name);
      if (!functionSchema) {
        throw new Error(`Unknown function: ${functionCall.name}. Available functions: ${this.getAvailableFunctionNames().join(', ')}`);
      }

      // Validate required parameters
      const missingParams = functionSchema.parameters.required.filter(param => !(param in args));
      if (missingParams.length > 0) {
        throw new Error(`Missing required parameters: ${missingParams.join(', ')}. Provided: ${Object.keys(args).join(', ')}`);
      }

      // Validate parameter types (basic validation)
      this.validateParameterTypes(args, functionSchema);

      console.log(`🔧 Executing function: ${functionCall.name} with args:`, JSON.stringify(args, null, 2));

      // Execute the appropriate function with timeout
      const result = await Promise.race([
        this.executeFunction(functionCall.name, args),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Function ${functionCall.name} timed out after ${this.timeout}ms`)), this.timeout)
        )
      ]);

      console.log(`✅ Function ${functionCall.name} completed successfully in ${Date.now() - startTime}ms`);

      return {
        success: true,
        result,
        functionName: functionCall.name,
        executionTime: Date.now() - startTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Function call error for ${functionCall.name}:`, errorMessage);

      // Log additional context for debugging
      console.error(`Function call context:`, {
        functionName: functionCall.name,
        arguments: functionCall.arguments,
        userId: this.userId,
        timeout: this.timeout,
        executionTime: Date.now() - startTime
      });

      return {
        success: false,
        error: errorMessage,
        functionName: functionCall.name,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Get list of available function names for error messages
   */
  private getAvailableFunctionNames(): string[] {
    const allFunctions = Object.values(TOOL_FUNCTION_MAP).flat();
    return allFunctions.map(func => func.name);
  }

  /**
   * Basic parameter type validation
   */
  private validateParameterTypes(args: Record<string, any>, schema: any): void {
    for (const [paramName, paramValue] of Object.entries(args)) {
      const paramSchema = schema.parameters.properties[paramName];
      if (!paramSchema) continue; // Skip validation for unknown parameters

      const expectedType = paramSchema.type;
      const actualType = Array.isArray(paramValue) ? 'array' : typeof paramValue;

      if (expectedType === 'string' && actualType !== 'string') {
        throw new Error(`Parameter '${paramName}' must be a string, got ${actualType}`);
      } else if (expectedType === 'number' && actualType !== 'number') {
        throw new Error(`Parameter '${paramName}' must be a number, got ${actualType}`);
      } else if (expectedType === 'array' && !Array.isArray(paramValue)) {
        throw new Error(`Parameter '${paramName}' must be an array, got ${actualType}`);
      }
    }
  }

  /**
   * Execute multiple function calls with error isolation and retry logic
   */
  async executeFunctionCalls(functionCalls: FunctionCall[]): Promise<FunctionCallResult[]> {
    if (!functionCalls || functionCalls.length === 0) {
      return [];
    }

    console.log(`🔧 Executing ${functionCalls.length} function calls:`, functionCalls.map(fc => fc.name));

    // Execute function calls with error isolation
    const results: FunctionCallResult[] = [];

    for (const functionCall of functionCalls) {
      try {
        const result = await this.executeFunctionCallWithRetry(functionCall);
        results.push(result);
      } catch (error) {
        // Even if individual function fails, continue with others
        console.error(`Failed to execute function ${functionCall.name}:`, error);
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          functionName: functionCall.name,
          executionTime: 0
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`✅ Function execution completed: ${successCount}/${results.length} successful`);

    return results;
  }

  /**
   * Execute function call with retry logic
   */
  private async executeFunctionCallWithRetry(functionCall: FunctionCall, maxRetries: number = 2): Promise<FunctionCallResult> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        const result = await this.executeFunctionCall(functionCall);

        // If successful, return immediately
        if (result.success) {
          if (attempt > 1) {
            console.log(`✅ Function ${functionCall.name} succeeded on attempt ${attempt}`);
          }
          return result;
        }

        // If not successful but no exception, treat as final result
        if (attempt === maxRetries + 1) {
          return result;
        }

        // Prepare for retry
        lastError = new Error(result.error || 'Function execution failed');
        console.warn(`⚠️ Function ${functionCall.name} failed on attempt ${attempt}, retrying...`);

        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt === maxRetries + 1) {
          throw lastError;
        }

        console.warn(`⚠️ Function ${functionCall.name} threw error on attempt ${attempt}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
      }
    }

    throw lastError || new Error('Function execution failed after all retries');
  }

  /**
   * Route function call to appropriate tool implementation
   */
  private async executeFunction(functionName: string, args: Record<string, any>): Promise<any> {
    switch (functionName) {
      // Google Docs Functions
      case 'create_google_document':
        return await this.createGoogleDocument(args);
      case 'update_google_document':
        return await this.updateGoogleDocument(args);
      case 'update_google_document_by_name':
        return await this.updateGoogleDocumentByName(args);
      case 'get_google_document':
        return await GoogleDocsAPI.getDocument(this.userId, args, this.timeout);
      case 'get_google_document_by_name':
        return await this.getGoogleDocumentByName(args);
      case 'list_google_documents':
        return await this.listGoogleDocuments(args);
      case 'format_google_document':
        return await this.formatGoogleDocument(args);
      case 'insert_table_google_document':
        return await this.insertTableGoogleDocument(args);
      case 'export_google_document':
        return await this.exportGoogleDocument(args);
      case 'replace_text_google_document':
        return await this.replaceTextGoogleDocument(args);
      case 'insert_image_google_document':
        return await this.insertImageGoogleDocument(args);
      case 'create_document_outline':
        return await this.createDocumentOutline(args);
      case 'apply_document_template':
        return await this.applyDocumentTemplate(args);

      // Google Drive Functions
      case 'list_google_drive_files':
        return await GoogleDriveAPI.listFiles(this.userId, args, this.timeout);
      case 'search_google_drive_files':
        return await GoogleDriveAPI.searchFiles(this.userId, args, this.timeout);
      case 'create_google_drive_file':
        return await GoogleDriveAPI.createFile(this.userId, args, this.timeout);

      // Google Sheets Functions
      case 'create_google_spreadsheet':
        return await GoogleSheetsAPI.createSpreadsheet(this.userId, args, this.timeout);
      case 'update_google_sheets_cells':
        return await GoogleSheetsAPI.updateCells(this.userId, args, this.timeout);
      case 'read_google_sheets_range':
        return await GoogleSheetsAPI.readRange(this.userId, args, this.timeout);

      // Gmail Functions
      case 'send_gmail_email':
        return await GmailAPI.sendEmail(this.userId, args, this.timeout);
      case 'list_gmail_emails':
        return await GmailAPI.listEmails(this.userId, args, this.timeout);
      case 'search_gmail_emails':
        return await GmailAPI.searchEmails(this.userId, args, this.timeout);

      // Google Calendar Functions
      case 'create_calendar_event':
        return await GoogleCalendarAPI.createEvent(this.userId, args, this.timeout);
      case 'list_calendar_events':
        return await GoogleCalendarAPI.listEvents(this.userId, args, this.timeout);

      // YouTube Functions
      case 'search_youtube_videos':
        return await YouTubeAPI.searchVideos(this.userId, args, this.timeout);
      case 'get_youtube_video_details':
        return await YouTubeAPI.getVideoDetails(this.userId, args, this.timeout);

      // Notion Functions
      case 'create_notion_page':
        return await this.createNotionPage(args);
      case 'update_notion_page':
        return await this.updateNotionPage(args);
      case 'search_notion_pages':
        return await NotionAPI.queryDatabase(this.userId, args, this.timeout);

      // Web Browsing Functions
      case 'search_web':
        return await this.searchWeb(args);
      case 'navigate_to_url':
        return await this.navigateToUrl(args);
      case 'take_screenshot':
        return await this.takeScreenshot(args);
      case 'execute_custom_browsing':
        return await this.executeCustomBrowsing(args);

      default:
        throw new Error(`Function not implemented: ${functionName}`);
    }
  }

  /**
   * Enhanced Google Docs creation with content support
   */
  private async createGoogleDocument(args: Record<string, any>): Promise<any> {
    const { title, content } = args;
    
    // Create the document
    const createResult = await GoogleDocsAPI.createDocument(this.userId, { title }, this.timeout);
    
    if (!createResult.success || !createResult.document) {
      return createResult;
    }

    const documentId = createResult.document.documentId;
    const documentUrl = `https://docs.google.com/document/d/${documentId}/edit`;

    // Add content if provided
    if (content) {
      // For new documents, we can safely insert at index 1
      // New Google Docs start with just a newline character at index 1
      const updateRequests = [
        {
          insertText: {
            location: { index: 1 },
            text: content
          }
        }
      ];

      const updateResult = await GoogleDocsAPI.updateDocument(this.userId, {
        documentId,
        requests: updateRequests
      }, this.timeout);

      if (!updateResult.success) {
        console.warn('Document created but content update failed:', updateResult.error);
      }
    }

    return {
      success: true,
      document: createResult.document,
      documentUrl,
      title,
      content: content || '',
      message: `Successfully created Google Doc: "${title}"`
    };
  }

  /**
   * Helper function to safely calculate insertion index for Google Docs
   */
  private async getSafeInsertionIndex(documentId: string, requestedIndex?: number): Promise<number> {
    if (requestedIndex !== undefined) {
      return requestedIndex;
    }

    try {
      const docResult = await GoogleDocsAPI.getDocument(this.userId, { documentId }, this.timeout);
      if (docResult.success && docResult.document) {
        const docContent = docResult.document.body?.content;
        if (docContent && docContent.length > 0) {
          const lastElement = docContent[docContent.length - 1];
          const documentEndIndex = lastElement?.endIndex || 2;
          return Math.max(1, documentEndIndex - 1);
        }
      }
    } catch (error) {
      console.warn('Could not get document structure for safe insertion, using default index:', error);
    }

    return 1; // Safe default
  }

  /**
   * Enhanced Google Docs update with flexible content insertion
   */
  private async updateGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, content, insertIndex } = args;

    // Use helper function to get safe insertion index
    const finalInsertIndex = await this.getSafeInsertionIndex(documentId, insertIndex);

    const updateRequests = [
      {
        insertText: {
          location: { index: finalInsertIndex },
          text: content
        }
      }
    ];

    const result = await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests: updateRequests
    }, this.timeout);

    if (result.success) {
      return {
        ...result,
        documentUrl: `https://docs.google.com/document/d/${documentId}/edit`,
        message: 'Document updated successfully'
      };
    }

    return result;
  }

  /**
   * Update Google Document by name - find document by name and add content
   */
  private async updateGoogleDocumentByName(args: Record<string, any>): Promise<any> {
    const { documentName, content, insertIndex } = args;

    try {
      // First, search for the document by name
      const searchResult = await this.listGoogleDocuments({ query: documentName });

      if (!searchResult.success || !searchResult.documents || searchResult.documents.length === 0) {
        return {
          success: false,
          error: `No document found with name "${documentName}"`
        };
      }

      // Get the first matching document
      const document = searchResult.documents[0];
      const documentId = document.id;

      // Use helper function to get safe insertion index
      const finalInsertIndex = await this.getSafeInsertionIndex(documentId, insertIndex);

      console.log(`📝 Updating document "${documentName}" at index ${finalInsertIndex}`);
      console.log(`📝 Document ID: ${documentId}, Content length: ${content?.length || 0}`);

      // Update the document with the content
      const updateRequests = [
        {
          insertText: {
            location: { index: finalInsertIndex },
            text: content
          }
        }
      ];

      const result = await GoogleDocsAPI.updateDocument(this.userId, {
        documentId,
        requests: updateRequests
      }, this.timeout);

      if (result.success) {
        return {
          ...result,
          documentUrl: `https://docs.google.com/document/d/${documentId}/edit`,
          documentName,
          message: `Successfully updated document "${documentName}" with new content`
        };
      }

      // Log the specific error for debugging
      console.error(`❌ Failed to update document "${documentName}":`, result.error);
      return result;
    } catch (error) {
      console.error('Error updating Google Document by name:', error);

      // Check if it's an insertion index error
      if (error instanceof Error && error.message.includes('Index') && error.message.includes('must be less than')) {
        return {
          success: false,
          error: `Document insertion failed: Invalid insertion index. This usually happens when trying to insert content at the end of a document. The document structure may have changed.`
        };
      }

      return {
        success: false,
        error: `Failed to update document "${documentName}": ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Search Google Docs using Drive API with dynamic authentication
   */
  private async searchGoogleDocsViaDrive(searchParams: any, connectedTools: string[]): Promise<any> {
    const { authenticatedGet } = await import('@/lib/oauth/middleware');

    // Determine which Google tool authentication to use based on connected tools
    let authToolType = 'google_docs'; // Default preference

    if (!connectedTools.includes('google_docs')) {
      if (connectedTools.includes('google_drive')) {
        authToolType = 'google_drive';
      } else {
        throw new Error('No Google authentication available. Please connect Google Docs or Google Drive.');
      }
    }

    console.log(`🔐 Using ${authToolType} authentication for Google Docs listing`);

    const url = 'https://www.googleapis.com/drive/v3/files';
    const queryParams = new URLSearchParams({
      q: `mimeType='application/vnd.google-apps.document'${searchParams.query ? ` and ${searchParams.query}` : ''}`,
      pageSize: searchParams.maxResults?.toString() || '20',
      fields: 'files(id,name,mimeType,createdTime,modifiedTime,webViewLink)'
    });

    try {
      const response = await authenticatedGet(`${url}?${queryParams}`, {
        userId: this.userId,
        toolType: authToolType, // Use dynamic authentication
        timeout: this.timeout
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`🔐 Google Drive API error (${response.status}):`, errorText);

        if (response.status === 401 || response.status === 403) {
          throw new Error(`Google authentication failed. Please reconnect your Google account with the necessary permissions to access Google Drive.`);
        }

        throw new Error(`Google Drive API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      return {
        success: true,
        files: data.files || [],
        count: data.files?.length || 0
      };
    } catch (error) {
      console.error('🔐 Google Docs listing error:', error);
      throw error;
    }
  }

  /**
   * List Google Documents using Google Drive API
   */
  private async listGoogleDocuments(args: Record<string, any>): Promise<any> {
    const { query, maxResults = 20 } = args;

    // Use Google Drive API to list documents
    const searchParams: any = {
      mimeType: 'application/vnd.google-apps.document',
      maxResults
    };

    if (query) {
      searchParams.query = `name contains '${query}'`;
    }

    try {
      // Get connected tools to determine authentication method
      const connectedTools = await this.getConnectedTools();
      const result = await this.searchGoogleDocsViaDrive(searchParams, connectedTools);

      if (result.success && result.files) {
        // Format the results to include document URLs
        const documents = result.files.map((file: any) => ({
          id: file.id,
          name: file.name,
          url: `https://docs.google.com/document/d/${file.id}/edit`,
          createdTime: file.createdTime,
          modifiedTime: file.modifiedTime
        }));

        console.log(`🔍 Google Docs search result: Found ${documents.length} documents for query "${query}"`);

        return {
          success: true,
          documents,
          message: `Found ${documents.length} Google Documents${query ? ` matching "${query}"` : ''}`
        };
      }

      console.log(`🔍 Google Docs search result (no files):`, result);
      return result;
    } catch (error) {
      console.error('🔐 List Google Documents error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      return {
        success: false,
        error: `Failed to list Google Documents: ${errorMessage}`,
        documents: [],
        message: `Error: ${errorMessage}`
      };
    }
  }

  /**
   * Enhanced Notion page creation
   */
  private async createNotionPage(args: Record<string, any>): Promise<any> {
    const { title, content, parentPageId } = args;

    const pageData = {
      parent: parentPageId 
        ? { type: 'page_id', page_id: parentPageId }
        : { type: 'page_id', page_id: 'default' }, // This would need proper configuration
      properties: {
        title: {
          title: [{ text: { content: title } }]
        }
      },
      ...(content && {
        children: [
          {
            object: 'block',
            type: 'paragraph',
            paragraph: {
              rich_text: [{ text: { content } }]
            }
          }
        ]
      })
    };

    const result = await NotionAPI.createPage(this.userId, pageData, this.timeout);

    if (result.success && result.page) {
      return {
        ...result,
        pageUrl: result.page.url,
        title,
        message: `Successfully created Notion page: "${title}"`
      };
    }

    return result;
  }

  /**
   * Enhanced Notion page update
   */
  private async updateNotionPage(args: Record<string, any>): Promise<any> {
    const { pageId, content } = args;

    // This would need to be implemented based on Notion's block update API
    // For now, return a placeholder
    return {
      success: false,
      error: 'Notion page update not yet fully implemented'
    };
  }

  /**
   * Web Browsing Functions
   */
  private async searchWeb(args: Record<string, any>): Promise<any> {
    const { query, searchEngine = 'google', maxResults = 10 } = args;

    const result = await WebBrowsingTool.search(query, {
      searchEngine,
      extractionType: 'search'
    });

    if (!result.success) {
      throw new Error(`Web search failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      results: result.data,
      query,
      searchEngine,
      timestamp: result.timestamp
    };
  }

  private async navigateToUrl(args: Record<string, any>): Promise<any> {
    const { url, extractionType = 'content', customSelector } = args;

    const result = await WebBrowsingTool.navigate(url, {
      extractionType,
      customSelector
    });

    if (!result.success) {
      throw new Error(`Navigation failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      content: result.data,
      url,
      extractionType,
      timestamp: result.timestamp
    };
  }

  private async takeScreenshot(args: Record<string, any>): Promise<any> {
    const { url, fullPage = false } = args;

    const result = await WebBrowsingTool.takeScreenshot(url, {
      extractionType: 'screenshot'
    });

    if (!result.success) {
      throw new Error(`Screenshot failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      screenshot: result.data,
      url,
      fullPage,
      timestamp: result.timestamp
    };
  }

  private async executeCustomBrowsing(args: Record<string, any>): Promise<any> {
    const { url, code, context = {} } = args;

    const result = await WebBrowsingTool.executeCustom(url, code, context, {
      extractionType: 'custom'
    });

    if (!result.success) {
      throw new Error(`Custom browsing failed: ${result.error || 'Unknown error'}`);
    }

    return {
      success: true,
      result: result.data,
      url,
      code,
      context,
      timestamp: result.timestamp
    };
  }

  /**
   * Get Google Document by name with intelligent summarization
   */
  private async getGoogleDocumentByName(args: Record<string, any>): Promise<any> {
    const { documentName, summaryType = 'detailed' } = args;

    try {
      // First, search for the document by name
      const searchResult = await this.listGoogleDocuments({ query: documentName });

      if (!searchResult.success || !searchResult.documents || searchResult.documents.length === 0) {
        return {
          success: false,
          error: `No document found with name "${documentName}"`
        };
      }

      // Get the first matching document
      const document = searchResult.documents[0];
      const documentId = document.id;

      // Retrieve the document content
      const contentResult = await GoogleDocsAPI.getDocument(this.userId, { documentId }, this.timeout);

      if (contentResult.success) {
        // Extract readable text from the Google Docs structure
        const readableText = this.extractTextFromGoogleDoc(contentResult.document);

        // Generate intelligent summary based on summaryType
        const processedContent = await this.processDocumentContent(readableText, summaryType, document.name, document.url);

        return {
          success: true,
          documentName: document.name,
          documentUrl: document.url,
          documentId: documentId,
          content: processedContent,
          summaryType: summaryType,
          originalLength: readableText.length,
          message: `Successfully retrieved and ${summaryType === 'full_content' ? 'extracted' : 'summarized'} content for "${documentName}"`,
          textContent: processedContent
        };
      }

      return contentResult;
    } catch (error) {
      return {
        success: false,
        error: `Failed to retrieve document "${documentName}": ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Format text in Google Document
   */
  private async formatGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, startIndex, endIndex, formatting } = args;

    const requests = [];

    // Build formatting requests
    if (formatting.bold !== undefined || formatting.italic !== undefined || formatting.underline !== undefined) {
      requests.push({
        updateTextStyle: {
          range: {
            startIndex,
            endIndex
          },
          textStyle: {
            bold: formatting.bold,
            italic: formatting.italic,
            underline: formatting.underline,
            ...(formatting.fontSize && { fontSize: { magnitude: formatting.fontSize, unit: 'PT' } }),
            ...(formatting.foregroundColor && { foregroundColor: { color: { rgbColor: this.parseColor(formatting.foregroundColor) } } }),
            ...(formatting.backgroundColor && { backgroundColor: { color: { rgbColor: this.parseColor(formatting.backgroundColor) } } })
          },
          fields: Object.keys(formatting).join(',')
        }
      });
    }

    // Handle heading levels
    if (formatting.headingLevel) {
      requests.push({
        updateParagraphStyle: {
          range: {
            startIndex,
            endIndex
          },
          paragraphStyle: {
            namedStyleType: `HEADING_${formatting.headingLevel}`
          },
          fields: 'namedStyleType'
        }
      });
    }

    return await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests
    }, this.timeout);
  }

  /**
   * Insert table into Google Document
   */
  private async insertTableGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, insertIndex, rows, columns, tableData } = args;

    // Use helper function to get safe insertion index
    const safeInsertIndex = await this.getSafeInsertionIndex(documentId, insertIndex);

    const requests = [
      {
        insertTable: {
          location: {
            index: safeInsertIndex
          },
          rows,
          columns
        }
      }
    ];

    // If table data is provided, populate the table
    if (tableData && Array.isArray(tableData)) {
      let currentIndex = safeInsertIndex + 1; // Start after table insertion

      for (let row = 0; row < Math.min(tableData.length, rows); row++) {
        const rowData = tableData[row];
        if (Array.isArray(rowData)) {
          for (let col = 0; col < Math.min(rowData.length, columns); col++) {
            const cellText = String(rowData[col] || '');
            if (cellText) {
              requests.push({
                insertText: {
                  location: {
                    index: currentIndex
                  },
                  text: cellText
                }
              });
              currentIndex += cellText.length;
            }
            currentIndex += 1; // Move to next cell
          }
        }
      }
    }

    return await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests
    }, this.timeout);
  }

  /**
   * Export Google Document to various formats
   */
  private async exportGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, format } = args;

    try {
      const { authenticatedGet } = await import('@/lib/oauth/middleware');

      // Map format to MIME type
      const mimeTypes: Record<string, string> = {
        pdf: 'application/pdf',
        docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        odt: 'application/vnd.oasis.opendocument.text',
        rtf: 'application/rtf',
        txt: 'text/plain',
        html: 'text/html'
      };

      const mimeType = mimeTypes[format];
      if (!mimeType) {
        return {
          success: false,
          error: `Unsupported export format: ${format}`
        };
      }

      const url = `https://docs.googleapis.com/v1/documents/${documentId}/export?mimeType=${encodeURIComponent(mimeType)}`;

      const response = await authenticatedGet(url, {
        userId: this.userId,
        toolType: 'google_docs',
        timeout: this.timeout
      });

      if (!response.ok) {
        throw new Error(`Export failed: ${response.status}`);
      }

      // For now, return the export URL - in a full implementation, you'd handle the binary data
      return {
        success: true,
        exportUrl: url,
        format,
        message: `Document exported to ${format.toUpperCase()} format`,
        note: 'Use the exportUrl to download the file directly'
      };
    } catch (error) {
      return {
        success: false,
        error: `Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Replace text in Google Document
   */
  private async replaceTextGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, findText, replaceText, matchCase = false } = args;

    const requests = [
      {
        replaceAllText: {
          containsText: {
            text: findText,
            matchCase
          },
          replaceText
        }
      }
    ];

    return await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests
    }, this.timeout);
  }

  /**
   * Insert image into Google Document
   */
  private async insertImageGoogleDocument(args: Record<string, any>): Promise<any> {
    const { documentId, insertIndex, imageUrl, width, height } = args;

    // Use helper function to get safe insertion index
    const safeInsertIndex = await this.getSafeInsertionIndex(documentId, insertIndex);

    const requests = [
      {
        insertInlineImage: {
          location: {
            index: safeInsertIndex
          },
          uri: imageUrl,
          ...(width && height && {
            objectSize: {
              width: { magnitude: width, unit: 'PT' },
              height: { magnitude: height, unit: 'PT' }
            }
          })
        }
      }
    ];

    return await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests
    }, this.timeout);
  }

  /**
   * Create document outline/table of contents
   */
  private async createDocumentOutline(args: Record<string, any>): Promise<any> {
    const { documentId, insertIndex } = args;

    // Use helper function to get safe insertion index
    const safeInsertIndex = await this.getSafeInsertionIndex(documentId, insertIndex);

    // First, get the document to analyze its structure
    const docResult = await GoogleDocsAPI.getDocument(this.userId, { documentId }, this.timeout);

    if (!docResult.success) {
      return docResult;
    }

    // Generate outline based on headings in the document
    const requests = [
      {
        insertText: {
          location: { index: safeInsertIndex },
          text: "Table of Contents\n\n"
        }
      },
      {
        updateParagraphStyle: {
          range: {
            startIndex: safeInsertIndex,
            endIndex: safeInsertIndex + 19
          },
          paragraphStyle: {
            namedStyleType: 'HEADING_1'
          },
          fields: 'namedStyleType'
        }
      }
    ];

    return await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests
    }, this.timeout);
  }

  /**
   * Apply professional template to document
   */
  private async applyDocumentTemplate(args: Record<string, any>): Promise<any> {
    const { documentId, templateType } = args;

    const templates: Record<string, any[]> = {
      business_letter: [
        {
          updateDocumentStyle: {
            documentStyle: {
              marginTop: { magnitude: 72, unit: 'PT' },
              marginBottom: { magnitude: 72, unit: 'PT' },
              marginLeft: { magnitude: 72, unit: 'PT' },
              marginRight: { magnitude: 72, unit: 'PT' }
            },
            fields: 'marginTop,marginBottom,marginLeft,marginRight'
          }
        }
      ],
      report: [
        {
          updateDocumentStyle: {
            documentStyle: {
              marginTop: { magnitude: 72, unit: 'PT' },
              marginBottom: { magnitude: 72, unit: 'PT' },
              marginLeft: { magnitude: 90, unit: 'PT' },
              marginRight: { magnitude: 90, unit: 'PT' }
            },
            fields: 'marginTop,marginBottom,marginLeft,marginRight'
          }
        }
      ],
      academic_paper: [
        {
          updateDocumentStyle: {
            documentStyle: {
              marginTop: { magnitude: 72, unit: 'PT' },
              marginBottom: { magnitude: 72, unit: 'PT' },
              marginLeft: { magnitude: 72, unit: 'PT' },
              marginRight: { magnitude: 72, unit: 'PT' }
            },
            fields: 'marginTop,marginBottom,marginLeft,marginRight'
          }
        }
      ]
    };

    const requests = templates[templateType] || templates.business_letter;

    return await GoogleDocsAPI.updateDocument(this.userId, {
      documentId,
      requests
    }, this.timeout);
  }

  /**
   * Extract readable text from Google Docs API response
   */
  private extractTextFromGoogleDoc(document: any): string {
    if (!document || !document.body || !document.body.content) {
      return '';
    }

    let text = '';

    for (const element of document.body.content) {
      if (element.paragraph && element.paragraph.elements) {
        for (const paragraphElement of element.paragraph.elements) {
          if (paragraphElement.textRun && paragraphElement.textRun.content) {
            text += paragraphElement.textRun.content;
          }
        }
      } else if (element.table) {
        // Handle tables
        for (const row of element.table.tableRows || []) {
          for (const cell of row.tableCells || []) {
            for (const cellElement of cell.content || []) {
              if (cellElement.paragraph && cellElement.paragraph.elements) {
                for (const paragraphElement of cellElement.paragraph.elements) {
                  if (paragraphElement.textRun && paragraphElement.textRun.content) {
                    text += paragraphElement.textRun.content;
                  }
                }
              }
            }
            text += '\t'; // Tab between cells
          }
          text += '\n'; // New line after each row
        }
      }
    }

    return text.trim();
  }

  /**
   * Process document content based on summary type using AI
   */
  private async processDocumentContent(content: string, summaryType: string, documentName: string, documentUrl?: string): Promise<string> {
    // If full content requested or content is short, return as-is
    if (summaryType === 'full_content' || content.length <= 2000) {
      return content;
    }

    try {
      // Use Gemini for intelligent summarization
      const { executeProviderRequest } = await import('@/lib/providers/executeProviderRequest');

      const summaryPrompt = summaryType === 'brief'
        ? `Provide a brief summary of this document "${documentName}" with key points only (max 300 words):\n\n${content}`
        : `Provide a detailed summary of this document "${documentName}" with comprehensive key points, main sections, and important details (max 800 words):\n\n${content}`;

      const response = await executeProviderRequest({
        provider: 'gemini',
        model: 'gemini-2.0-flash-exp',
        messages: [{ role: 'user', content: summaryPrompt }],
        temperature: 0.2,
        max_tokens: summaryType === 'brief' ? 400 : 1000
      });

      if (response.success && response.responseData?.choices?.[0]?.message?.content) {
        const summary = response.responseData.choices[0].message.content;
        return `📄 **${documentName}** - ${summaryType === 'brief' ? 'Brief' : 'Detailed'} Summary\n\n${summary}\n\n🔗 **Document Link**: [Open in Google Docs](${documentUrl || 'https://docs.google.com/document'})`;
      }

      // Fallback to simple truncation if AI summarization fails
      return this.createFallbackSummary(content, summaryType, documentName, documentUrl);

    } catch (error) {
      console.error('Document summarization error:', error);
      return this.createFallbackSummary(content, summaryType, documentName, documentUrl);
    }
  }

  /**
   * Create fallback summary when AI summarization fails
   */
  private createFallbackSummary(content: string, summaryType: string, documentName: string, documentUrl?: string): string {
    const maxLength = summaryType === 'brief' ? 500 : 1500;
    const truncated = content.length > maxLength ? content.substring(0, maxLength) + '...' : content;

    return `📄 **${documentName}** - ${summaryType === 'brief' ? 'Brief' : 'Detailed'} Content\n\n${truncated}\n\n🔗 **Document Link**: [Open in Google Docs](${documentUrl || 'https://docs.google.com/document'})`;
  }

  /**
   * Helper method to parse color strings
   */
  private parseColor(colorString: string): { red: number; green: number; blue: number } {
    // Handle hex colors
    if (colorString.startsWith('#')) {
      const hex = colorString.slice(1);
      return {
        red: parseInt(hex.slice(0, 2), 16) / 255,
        green: parseInt(hex.slice(2, 4), 16) / 255,
        blue: parseInt(hex.slice(4, 6), 16) / 255
      };
    }

    // Default to black
    return { red: 0, green: 0, blue: 0 };
  }
}

/**
 * Format function call results for AI model consumption
 */
export function formatFunctionCallResults(results: FunctionCallResult[]): string {
  return results.map(result => {
    if (result.success) {
      // Format results in a more natural way for AI consumption
      if (result.functionName.includes('google_document')) {
        const docResult = result.result;
        if (docResult.documentUrl && docResult.title) {
          return `Successfully ${result.functionName.includes('create') ? 'created' : 'updated'} Google Document "${docResult.title}". Document is available at: ${docResult.documentUrl}`;
        } else if (docResult.message) {
          return docResult.message;
        }
      } else if (result.functionName.includes('google_drive')) {
        const driveResult = result.result;
        if (driveResult.webViewLink && driveResult.name) {
          return `Successfully processed file "${driveResult.name}" in Google Drive. File is available at: ${driveResult.webViewLink}`;
        }
      } else if (result.functionName.includes('gmail')) {
        return `Email operation completed successfully.`;
      } else if (result.functionName.includes('calendar')) {
        return `Calendar operation completed successfully.`;
      }

      // Generic success message for other functions
      return `${result.functionName.replace(/_/g, ' ')} completed successfully.`;
    } else {
      return `${result.functionName.replace(/_/g, ' ')} failed: ${result.error}`;
    }
  }).join('\n\n');
}

/**
 * Check if a response contains function calls (OpenAI format)
 */
export function hasFunctionCalls(response: any): boolean {
  return response?.choices?.[0]?.message?.tool_calls?.length > 0 ||
         response?.choices?.[0]?.message?.function_call;
}

/**
 * Extract function calls from AI response (OpenAI format)
 */
export function extractFunctionCalls(response: any): FunctionCall[] {
  const message = response?.choices?.[0]?.message;
  const functionCalls: FunctionCall[] = [];

  // Handle new tool_calls format
  if (message?.tool_calls) {
    for (const toolCall of message.tool_calls) {
      if (toolCall.type === 'function') {
        functionCalls.push({
          name: toolCall.function.name,
          arguments: toolCall.function.arguments
        });
      }
    }
  }

  // Handle legacy function_call format
  if (message?.function_call) {
    functionCalls.push({
      name: message.function_call.name,
      arguments: message.function_call.arguments
    });
  }

  return functionCalls;
}
