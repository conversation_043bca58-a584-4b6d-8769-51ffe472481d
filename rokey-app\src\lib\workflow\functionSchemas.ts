// Function Calling Schemas for AI Models
// This defines all available tools as proper function schemas for OpenAI, Anthropic, etc.

export interface FunctionSchema {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
}

// Google Docs Functions
export const googleDocsFunctions: FunctionSchema[] = [
  {
    name: "create_google_document",
    description: "Create a NEW Google Document with specified title and optional content. Only use this if you need to create a completely new document.",
    parameters: {
      type: "object",
      properties: {
        title: {
          type: "string",
          description: "The title of the document to create"
        },
        content: {
          type: "string",
          description: "Optional initial content for the document"
        }
      },
      required: ["title"]
    }
  },
  {
    name: "update_google_document",
    description: "Add content to an EXISTING Google Document. Use this when the user wants to write to a document that already exists.",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document to update"
        },
        content: {
          type: "string",
          description: "Content to add to the document"
        },
        insertIndex: {
          type: "number",
          description: "Position to insert content (default: end of document)"
        }
      },
      required: ["documentId", "content"]
    }
  },
  {
    name: "get_google_document",
    description: "Retrieve content from an existing Google Document. Use this to read document content.",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document to retrieve"
        }
      },
      required: ["documentId"]
    }
  },
  {
    name: "get_google_document_by_name",
    description: "Find and intelligently summarize a Google Document by its name/title. Provides smart summaries with key points and document links for easy access.",
    parameters: {
      type: "object",
      properties: {
        documentName: {
          type: "string",
          description: "The name/title of the document to find and retrieve"
        },
        summaryType: {
          type: "string",
          enum: ["brief", "detailed", "full_content"],
          description: "Type of summary: 'brief' (key points only), 'detailed' (comprehensive summary), 'full_content' (complete text)",
          default: "detailed"
        }
      },
      required: ["documentName"]
    }
  },
  {
    name: "update_google_document_by_name",
    description: "Find an existing Google Document by name and add content to it. Use this when the user wants to write to a document that already exists and you know the document name but not the ID.",
    parameters: {
      type: "object",
      properties: {
        documentName: {
          type: "string",
          description: "The name/title of the document to find and update"
        },
        content: {
          type: "string",
          description: "Content to add to the document"
        },
        insertIndex: {
          type: "number",
          description: "Position to insert content (default: end of document)"
        }
      },
      required: ["documentName", "content"]
    }
  },
  {
    name: "format_google_document",
    description: "Apply formatting to text in a Google Document (bold, italic, underline, headers, etc.)",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document to format"
        },
        startIndex: {
          type: "number",
          description: "Start position of text to format"
        },
        endIndex: {
          type: "number",
          description: "End position of text to format"
        },
        formatting: {
          type: "object",
          description: "Formatting options",
          properties: {
            bold: { type: "boolean" },
            italic: { type: "boolean" },
            underline: { type: "boolean" },
            fontSize: { type: "number" },
            foregroundColor: { type: "string" },
            backgroundColor: { type: "string" },
            headingLevel: { type: "number", description: "1-6 for heading levels" }
          }
        }
      },
      required: ["documentId", "startIndex", "endIndex", "formatting"]
    }
  },
  {
    name: "insert_table_google_document",
    description: "Insert a table into a Google Document",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document"
        },
        insertIndex: {
          type: "number",
          description: "Position to insert the table"
        },
        rows: {
          type: "number",
          description: "Number of rows"
        },
        columns: {
          type: "number",
          description: "Number of columns"
        },
        tableData: {
          type: "array",
          description: "Optional 2D array of table data",
          items: {
            type: "array",
            items: { type: "string" }
          }
        }
      },
      required: ["documentId", "insertIndex", "rows", "columns"]
    }
  },
  {
    name: "export_google_document",
    description: "Export a Google Document to PDF or DOCX format and get download link",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document to export"
        },
        format: {
          type: "string",
          enum: ["pdf", "docx", "odt", "rtf", "txt", "html"],
          description: "Export format"
        }
      },
      required: ["documentId", "format"]
    }
  },
  {
    name: "replace_text_google_document",
    description: "Find and replace text in a Google Document",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document"
        },
        findText: {
          type: "string",
          description: "Text to find"
        },
        replaceText: {
          type: "string",
          description: "Text to replace with"
        },
        matchCase: {
          type: "boolean",
          description: "Whether to match case (default: false)"
        }
      },
      required: ["documentId", "findText", "replaceText"]
    }
  },
  {
    name: "insert_image_google_document",
    description: "Insert an image into a Google Document from a URL",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document"
        },
        insertIndex: {
          type: "number",
          description: "Position to insert the image"
        },
        imageUrl: {
          type: "string",
          description: "URL of the image to insert"
        },
        width: {
          type: "number",
          description: "Image width in points (optional)"
        },
        height: {
          type: "number",
          description: "Image height in points (optional)"
        }
      },
      required: ["documentId", "insertIndex", "imageUrl"]
    }
  },
  {
    name: "create_document_outline",
    description: "Generate a structured outline/table of contents for a Google Document",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document"
        },
        insertIndex: {
          type: "number",
          description: "Position to insert the outline"
        }
      },
      required: ["documentId", "insertIndex"]
    }
  },
  {
    name: "apply_document_template",
    description: "Apply a professional template/styling to a Google Document",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document"
        },
        templateType: {
          type: "string",
          enum: ["business_letter", "report", "resume", "academic_paper", "newsletter"],
          description: "Type of template to apply"
        }
      },
      required: ["documentId", "templateType"]
    }
  },
  {
    name: "list_google_documents",
    description: "List recent Google Documents to find existing documents by name",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Optional search query to filter documents by name"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of documents to return (default: 20)"
        }
      },
      required: []
    }
  },
  {
    name: "delete_google_document",
    description: "Delete a Google Document by its ID. Use with caution as this action cannot be undone.",
    parameters: {
      type: "object",
      properties: {
        documentId: {
          type: "string",
          description: "The ID of the document to delete"
        }
      },
      required: ["documentId"]
    }
  },
  {
    name: "delete_google_document_by_name",
    description: "Find and delete a Google Document by its name/title. Use with caution as this action cannot be undone.",
    parameters: {
      type: "object",
      properties: {
        documentName: {
          type: "string",
          description: "The name/title of the document to find and delete"
        }
      },
      required: ["documentName"]
    }
  }
];

// Google Drive Functions
export const googleDriveFunctions: FunctionSchema[] = [
  {
    name: "list_google_drive_files",
    description: "List files in Google Drive with optional filtering",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query to filter files"
        },
        limit: {
          type: "number",
          description: "Maximum number of files to return (default: 10)"
        },
        mimeType: {
          type: "string",
          description: "Filter by specific MIME type"
        }
      },
      required: []
    }
  },
  {
    name: "search_google_drive_files",
    description: "Search for files in Google Drive by name or content",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search term to find files"
        },
        limit: {
          type: "number",
          description: "Maximum number of results (default: 10)"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "create_google_drive_file",
    description: "Create a new file in Google Drive",
    parameters: {
      type: "object",
      properties: {
        name: {
          type: "string",
          description: "Name of the file to create"
        },
        content: {
          type: "string",
          description: "Content of the file"
        },
        mimeType: {
          type: "string",
          description: "MIME type of the file (default: text/plain)"
        },
        parentFolderId: {
          type: "string",
          description: "ID of parent folder (optional)"
        }
      },
      required: ["name"]
    }
  }
];

// Google Sheets Functions
export const googleSheetsFunctions: FunctionSchema[] = [
  {
    name: "create_google_spreadsheet",
    description: "Create a new Google Spreadsheet",
    parameters: {
      type: "object",
      properties: {
        title: {
          type: "string",
          description: "Title of the spreadsheet"
        },
        sheetNames: {
          type: "array",
          items: { type: "string" },
          description: "Names of initial sheets to create"
        }
      },
      required: ["title"]
    }
  },
  {
    name: "update_google_sheets_cells",
    description: "Update cells in a Google Spreadsheet",
    parameters: {
      type: "object",
      properties: {
        spreadsheetId: {
          type: "string",
          description: "ID of the spreadsheet"
        },
        range: {
          type: "string",
          description: "Cell range to update (e.g., 'A1:B2')"
        },
        values: {
          type: "array",
          items: { type: "array", items: { type: "string" } },
          description: "2D array of values to insert"
        }
      },
      required: ["spreadsheetId", "range", "values"]
    }
  },
  {
    name: "read_google_sheets_range",
    description: "Read data from a range in Google Spreadsheet",
    parameters: {
      type: "object",
      properties: {
        spreadsheetId: {
          type: "string",
          description: "ID of the spreadsheet"
        },
        range: {
          type: "string",
          description: "Cell range to read (e.g., 'A1:B10')"
        }
      },
      required: ["spreadsheetId", "range"]
    }
  }
];

// Gmail Functions
export const gmailFunctions: FunctionSchema[] = [
  {
    name: "send_gmail_email",
    description: "Send an email through Gmail",
    parameters: {
      type: "object",
      properties: {
        to: {
          type: "string",
          description: "Recipient email address"
        },
        subject: {
          type: "string",
          description: "Email subject"
        },
        body: {
          type: "string",
          description: "Email body content"
        },
        cc: {
          type: "string",
          description: "CC recipients (optional)"
        },
        bcc: {
          type: "string",
          description: "BCC recipients (optional)"
        }
      },
      required: ["to", "subject", "body"]
    }
  },
  {
    name: "list_gmail_emails",
    description: "List emails from Gmail inbox",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Gmail search query"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of emails to return"
        },
        labelIds: {
          type: "array",
          items: { type: "string" },
          description: "Filter by label IDs"
        }
      },
      required: []
    }
  },
  {
    name: "search_gmail_emails",
    description: "Search emails in Gmail",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query (Gmail search syntax)"
        },
        maxResults: {
          type: "number",
          description: "Maximum results to return"
        }
      },
      required: ["query"]
    }
  }
];

// Google Calendar Functions
export const googleCalendarFunctions: FunctionSchema[] = [
  {
    name: "create_calendar_event",
    description: "Create a new event in Google Calendar",
    parameters: {
      type: "object",
      properties: {
        summary: {
          type: "string",
          description: "Event title/summary"
        },
        description: {
          type: "string",
          description: "Event description"
        },
        startDateTime: {
          type: "string",
          description: "Start date/time in ISO format"
        },
        endDateTime: {
          type: "string",
          description: "End date/time in ISO format"
        },
        attendees: {
          type: "array",
          items: { type: "string" },
          description: "List of attendee email addresses"
        },
        location: {
          type: "string",
          description: "Event location"
        }
      },
      required: ["summary", "startDateTime", "endDateTime"]
    }
  },
  {
    name: "list_calendar_events",
    description: "List upcoming events from Google Calendar",
    parameters: {
      type: "object",
      properties: {
        timeMin: {
          type: "string",
          description: "Start time for events (ISO format)"
        },
        timeMax: {
          type: "string",
          description: "End time for events (ISO format)"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of events"
        }
      },
      required: []
    }
  }
];

// YouTube Functions
export const youtubeFunctions: FunctionSchema[] = [
  {
    name: "search_youtube_videos",
    description: "Search for videos on YouTube",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query for videos"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of results (default: 10)"
        },
        order: {
          type: "string",
          enum: ["relevance", "date", "rating", "viewCount"],
          description: "Sort order for results"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "get_youtube_video_details",
    description: "Get detailed information about a YouTube video",
    parameters: {
      type: "object",
      properties: {
        videoId: {
          type: "string",
          description: "YouTube video ID"
        }
      },
      required: ["videoId"]
    }
  }
];

// Notion Functions
export const notionFunctions: FunctionSchema[] = [
  {
    name: "create_notion_page",
    description: "Create a new page in Notion",
    parameters: {
      type: "object",
      properties: {
        title: {
          type: "string",
          description: "Page title"
        },
        content: {
          type: "string",
          description: "Page content"
        },
        parentPageId: {
          type: "string",
          description: "ID of parent page (optional)"
        }
      },
      required: ["title"]
    }
  },
  {
    name: "update_notion_page",
    description: "Update an existing Notion page",
    parameters: {
      type: "object",
      properties: {
        pageId: {
          type: "string",
          description: "ID of the page to update"
        },
        content: {
          type: "string",
          description: "New content to add"
        }
      },
      required: ["pageId", "content"]
    }
  },
  {
    name: "search_notion_pages",
    description: "Search for pages in Notion",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "Search query"
        },
        filter: {
          type: "object",
          description: "Additional filters"
        }
      },
      required: ["query"]
    }
  }
];

// Web Browsing Functions
export const webBrowsingFunctions: FunctionSchema[] = [
  {
    name: "search_web",
    description: "Search the web using Google or Bing and return relevant results",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query to execute"
        },
        searchEngine: {
          type: "string",
          enum: ["google", "bing"],
          description: "Search engine to use (default: google)"
        },
        maxResults: {
          type: "number",
          description: "Maximum number of results to return (default: 10)"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "navigate_to_url",
    description: "Navigate to a specific URL and extract content from the webpage",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to navigate to"
        },
        extractionType: {
          type: "string",
          enum: ["content", "custom"],
          description: "Type of content extraction (default: content)"
        },
        customSelector: {
          type: "string",
          description: "CSS selector for custom content extraction"
        }
      },
      required: ["url"]
    }
  },
  {
    name: "take_screenshot",
    description: "Take a screenshot of a webpage",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to take a screenshot of"
        },
        fullPage: {
          type: "boolean",
          description: "Whether to capture the full page (default: false)"
        }
      },
      required: ["url"]
    }
  },
  {
    name: "execute_custom_browsing",
    description: "Execute custom JavaScript code on a webpage for advanced interactions",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to execute code on"
        },
        code: {
          type: "string",
          description: "JavaScript code to execute on the page"
        },
        context: {
          type: "object",
          description: "Additional context data for the code execution"
        }
      },
      required: ["url", "code"]
    }
  }
];

// Tool type mapping
export const TOOL_FUNCTION_MAP = {
  google_docs: googleDocsFunctions,
  google_drive: googleDriveFunctions,
  google_sheets: googleSheetsFunctions,
  gmail: gmailFunctions,
  calendar: googleCalendarFunctions,
  youtube: youtubeFunctions,
  notion: notionFunctions,
  web_browsing: webBrowsingFunctions
} as const;

// Get all functions for connected tools
export function getFunctionsForTools(connectedTools: string[]): FunctionSchema[] {
  const functions: FunctionSchema[] = [];
  
  for (const toolType of connectedTools) {
    if (toolType in TOOL_FUNCTION_MAP) {
      functions.push(...TOOL_FUNCTION_MAP[toolType as keyof typeof TOOL_FUNCTION_MAP]);
    }
  }
  
  return functions;
}

// Get function schema by name
export function getFunctionByName(functionName: string): FunctionSchema | undefined {
  const allFunctions = Object.values(TOOL_FUNCTION_MAP).flat();
  return allFunctions.find(func => func.name === functionName);
}
